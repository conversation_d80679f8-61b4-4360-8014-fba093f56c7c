<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>
      Daily Calorie Needs Calculator - Accurate BMR & TDEE Calculator |
      MeetAugust
    </title>
    <meta
      name="description"
      content="Calculate your daily calorie requirements with our advanced BMR calculator. Get personalized calorie goals for weight loss, maintenance, and muscle gain. Free, accurate, and science-based."
    />
    <meta
      name="keywords"
      content="calorie calculator, BMR calculator, TDEE calculator, daily calorie needs, weight loss calculator, metabolism calculator, nutrition planning"
    />
    <meta name="author" content="MeetAugust" />
    <meta
      property="og:title"
      content="Daily Calorie Needs Calculator - Accurate BMR & TDEE Calculator"
    />
    <meta
      property="og:description"
      content="Calculate your daily calorie requirements with our advanced BMR calculator. Get personalized calorie goals for weight loss, maintenance, and muscle gain."
    />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="Daily Calorie Needs Calculator - Accurate BMR & TDEE Calculator"
    />
    <meta
      name="twitter:description"
      content="Calculate your daily calorie requirements with our advanced BMR calculator. Get personalized calorie goals for weight loss, maintenance, and muscle gain."
    />
    <link rel="canonical" href="https://www.meetaugust.ai/calorie-calculator" />
    <style>
      :root {
        --primary-color: #416955;
        --primary-dark: #2d4a3a;
        --primary-light: #f0f9f4;
        --text-primary: #111827;
        --text-secondary: #374151;
        --text-muted: #6b7280;
        --border-color: #e5e7eb;
        --background-light: #f9fafb;
        --background-white: #ffffff;
        --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.05);
        --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.1);
        --border-radius: 8px;
        --border-radius-lg: 12px;
        --transition: all 0.2s ease;
      }

      * {
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f8f9fa;
        margin: 0;
        padding: 0;
        color: var(--text-primary);
        line-height: 1.7;
        font-size: 16px;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      #container {
        max-width: 1200px;
        margin: 0 auto;
      }
      header {
        background-color: transparent;
        padding: 4px 0px;
        border-bottom: none;
        position: sticky;
        top: 0;
        z-index: 1000;
        transition: all 0.3s ease;
      }

      .header-scrolled {
        background-color: #ffffff;
        padding: 4px 0px;
        border-bottom: 1px solid #e5e7eb;
        position: sticky;
        top: 0;
        z-index: 1000;
      }
      .navbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
        /* max-width: 1430px; */
        margin: 0 auto;
      }
      .logo {
        display: flex;
        align-items: center;
      }
      .logo img {
        height: 60px;
        width: auto;
      }
      .nav {
        display: flex;
        align-items: center;
        gap: 32px;
      }
      .nav-links {
        display: flex;
        align-items: center;
        gap: 24px;
        list-style: none;
        margin: 0;
        padding: 0;
      }
      .nav-links a {
        color: #111827;
        text-decoration: none;
        font-weight: 500;
        font-size: 16px;
        transition: color 0.2s ease;
      }
      .nav-links a:hover {
        color: #416955;
      }
      .nav-links a.active {
        color: #416955;
      }
      .talk-to-august {
        background-color: #416955;
        color: white !important;
        padding: 10px 18px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 500;
        font-size: 16px;
        transition: background-color 0.2s ease;
      }
      .talk-to-august:hover {
        background-color: #2d4a3a;
        color: white !important;
      }
      main {
        /* padding: 40px 20px; */
        /* max-width: 800px; */
        /* margin: 0 auto; */
        /* background-color: #ffffff; */
        /* border-radius: 8px; */
        /* box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); */
        margin-top: 20px;
        margin-bottom: 20px;
      }
      h1 {
        color: #416955;
        font-size: 36px;
        font-weight: 600;
        text-align: center;
        margin-bottom: 16px;
      }
      p {
        color: #1f2937;
        font-size: 18px;
        margin-bottom: 20px;
      }
      main > p {
        color: #1f2937;
        font-size: 18px;
        margin-bottom: 20px;
        text-align: center;
      }
      .print-link {
        text-align: right;
        margin-bottom: 10px;
      }
      .print-link a {
        color: #416955;
        text-decoration: none;
        font-weight: 500;
      }
      .print-link a:hover {
        color: #2d4a3a;
      }
      .form-container {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 30px;
        margin-top: 20px;
      }
      .tabs {
        margin-bottom: 10px;
      }
      .tab-button {
        background-color: #f3f4f6;
        border: 1px solid #d1d5db;
        padding: 10px 18px;
        cursor: pointer;
        border-radius: 6px 6px 0 0;
        font-weight: 500;
        font-size: 16px;
        color: #6b7280;
        transition: all 0.2s ease;
      }
      .tab-button:hover {
        background-color: #e5e7eb;
        color: #111827;
      }
      .tab-button.active {
        background-color: white;
        border-bottom: 2px solid #416955;
        color: #416955;
      }
      .form-instruction {
        color: #1f2937;
        font-size: 18px;
        margin-bottom: 16px;
      }
      .form-field {
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 12px;
      }
      .form-field label {
        display: inline-block;
        width: 100px;
        color: #111827;
        font-weight: 500;
        font-size: 16px;
      }
      .form-field input[type="text"] {
        width: 100px;
        background-color: white;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 10px 14px;
        font-size: 16px;
        transition: border-color 0.2s ease;
      }
      .form-field input[type="text"]:focus {
        outline: none;
        border-color: #416955;
        box-shadow: 0 0 0 3px rgba(65, 105, 85, 0.1);
      }
      .form-field select {
        width: 400px;
        background-color: white;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 10px 14px;
        font-size: 16px;
        transition: border-color 0.2s ease;
      }
      .form-field select:focus {
        outline: none;
        border-color: #416955;
        box-shadow: 0 0 0 3px rgba(65, 105, 85, 0.1);
      }
      .form-field input[type="radio"] {
        margin-right: 6px;
        accent-color: #416955;
      }
      .form-field span {
        color: #1f2937;
        font-size: 16px;
      }
      .settings-link {
        margin-bottom: 20px;
      }
      .settings-link a {
        color: #416955;
        text-decoration: none;
        font-weight: 500;
        font-size: 16px;
      }
      .settings-link a:hover {
        color: #2d4a3a;
      }
      .form-buttons {
        text-align: left;
        margin-top: 30px;
      }
      .calculate-button {
        background-color: #416955;
        color: white;
        padding: 14px 28px;
        border: none;
        border-radius: 6px;
        margin-right: 12px;
        cursor: pointer;
        font-weight: 500;
        font-size: 18px;
        transition: background-color 0.2s ease;
      }
      .calculate-button:hover {
        background-color: #2d4a3a;
      }
      .clear-button {
        background-color: #f3f4f6;
        color: #111827;
        padding: 14px 28px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        cursor: pointer;
        font-weight: 500;
        font-size: 18px;
        transition: all 0.2s ease;
      }
      .clear-button:hover {
        background-color: #e5e7eb;
        border-color: #9ca3af;
      }
      .result {
        margin-top: 30px;
        padding: 20px;
        background-color: #f0f9f4;
        border: 1px solid #bbf7d0;
        border-radius: 8px;
        color: #416955;
        font-size: 20px;
        font-weight: 600;
      }
      .activity-definitions {
        margin-top: 30px;
        padding: 20px;
        background-color: #f9fafb;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .activity-definitions h3 {
        color: #111827;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 12px;
      }
      .activity-definitions ul {
        list-style-type: disc;
        padding-left: 20px;
        color: #1f2937;
        font-size: 16px;
        line-height: 1.7;
      }
      .activity-definitions li {
        margin-bottom: 8px;
      }

      /* Converter Section Styles */
      .converter-section {
        margin-top: 40px;
        padding: 30px;
        background-color: #f9fafb;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .converter-section h2 {
        color: #416955;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      .converter-container {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-top: 20px;
        flex-wrap: wrap;
      }
      .converter-input,
      .converter-output {
        display: flex;
        align-items: center;
        gap: 12px;
      }
      .converter-input input {
        width: 100px;
        background-color: white;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 10px 14px;
        font-size: 16px;
      }
      .converter-input select,
      .converter-output select {
        background-color: white;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 10px 14px;
        font-size: 16px;
        min-width: 200px;
      }
      .converter-equals {
        font-size: 20px;
        font-weight: 600;
        color: #416955;
      }
      #converted-value {
        font-size: 20px;
        font-weight: 600;
        color: #416955;
        min-width: 80px;
      }

      /* Related Section Styles */
      .related-section {
        margin-top: 40px;
        padding: 30px;
        background-color: #f9fafb;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .related-section h2 {
        color: #416955;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 20px;
      }
      .related-links {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;
      }
      .related-link {
        background-color: #416955;
        color: white;
        padding: 14px 22px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 500;
        font-size: 16px;
        transition: background-color 0.2s ease;
      }
      .related-link:hover {
        background-color: #2d4a3a;
      }

      /* Information Section Styles */
      .info-section {
        margin-top: 40px;
        padding: 30px;
        background-color: #ffffff;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .info-section h2 {
        color: #416955;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      .info-section h3 {
        color: #111827;
        font-size: 22px;
        font-weight: 600;
        margin: 24px 0 16px 0;
      }
      .equations-container {
        margin-top: 24px;
      }
      .equation-card {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
      }
      .equation-card h4 {
        color: #416955;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 12px;
      }
      .equation-card p {
        margin-bottom: 8px;
        font-family: "Courier New", monospace;
        background-color: #f3f4f6;
        padding: 10px;
        border-radius: 4px;
        font-size: 16px;
      }
      .equation-note {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif !important;
        background-color: transparent !important;
        padding: 0 !important;
        font-style: italic;
        color: #374151;
        font-size: 16px;
      }
      .weight-guidance {
        background-color: #f0f9f4;
        border: 1px solid #bbf7d0;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;
      }
      .weight-guidance h4 {
        color: #416955;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 12px;
      }
      .weight-guidance ul {
        list-style-type: disc;
        padding-left: 20px;
        margin-bottom: 16px;
      }
      .weight-guidance li {
        margin-bottom: 8px;
        color: #111827;
      }
      .warning-note {
        background-color: #fef3c7;
        border: 1px solid #f59e0b;
        border-radius: 6px;
        padding: 12px;
        color: #92400e;
        font-weight: 500;
        margin-top: 16px;
      }

      /* Additional Info Sections */
      .info-text {
        margin-top: 20px;
      }
      .info-text p {
        background-color: transparent !important;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif !important;
        padding: 0 !important;
        margin-bottom: 16px;
        line-height: 1.6;
      }

      /* Counting Section Styles */
      .counting-section {
        margin-top: 40px;
        padding: 30px;
        background-color: #ffffff;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .counting-section h2 {
        color: #416955;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      .steps-container {
        display: grid;
        gap: 20px;
        margin-top: 24px;
      }
      .step-card {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 20px;
        border-left: 4px solid #416955;
      }
      .step-card h4 {
        color: #416955;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 12px;
      }

      /* Zigzag Section Styles */
      .zigzag-section {
        margin-top: 40px;
        padding: 30px;
        background-color: #ffffff;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .zigzag-section h2 {
        color: #416955;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      .zigzag-explanation {
        margin-top: 24px;
      }
      .zigzag-explanation h3 {
        color: #111827;
        font-size: 22px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      .example-card,
      .benefits-card {
        background-color: #f0f9f4;
        border: 1px solid #bbf7d0;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
      }
      .example-card h4,
      .benefits-card h4 {
        color: #416955;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 12px;
      }

      /* Requirements Section Styles */
      .requirements-section {
        margin-top: 40px;
        padding: 30px;
        background-color: #ffffff;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .requirements-section h2 {
        color: #416955;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      .factors-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 24px;
      }
      .factor-card,
      .guidelines-card,
      .minimum-card {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 20px;
      }
      .factor-card h4,
      .guidelines-card h4,
      .minimum-card h4 {
        color: #416955;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 12px;
      }
      .note {
        font-style: italic;
        color: #374151;
        font-size: 16px;
      }
      .warning {
        color: #dc2626;
        font-weight: 500;
      }

      /* Settings Section Styles */
      .settings-container {
        display: none;
        margin-top: 20px;
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 30px;
      }

      .settings-header {
        background-color: #416955;
        color: white;
        padding: 16px 20px;
        border-radius: 8px 8px 0 0;
        margin: -30px -30px 20px -30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .settings-header h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
      }

      .collapse-button {
        background: none;
        border: none;
        color: white;
        font-size: 18px;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background-color 0.2s ease;
      }

      .collapse-button:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }

      /* Enhanced Mobile Responsive Design */
      @media (max-width: 768px) {
        #container {
          max-width: 100%;
          margin: 0;
          padding: 0 12px;
        }

        main {
          margin: 15px 0;
          padding: 0;
        }

        .navbar {
          padding: 8px 12px;
          flex-wrap: wrap;
        }

        .navbar .logo img {
          height: 45px;
        }

        .navbar .nav-links {
          display: none;
        }

        .talk-to-august {
          padding: 8px 14px;
          font-size: 14px;
        }

        h1 {
          font-size: 24px;
          line-height: 1.3;
          margin-bottom: 12px;
          padding: 0 8px;
          text-align: center;
          color: #416955;
        }

        main > p {
          font-size: 15px;
          line-height: 1.5;
          margin-bottom: 18px;
          padding: 0 8px;
          text-align: center;
          color: #1f2937;
        }

        .form-container {
          padding: 20px 16px;
          margin: 15px 8px;
          border-radius: 12px;
          background-color: #ffffff;
          border: 1px solid #e5e7eb;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .form-instruction {
          font-size: 15px;
          margin-bottom: 18px;
          text-align: center;
          color: #374151;
          line-height: 1.4;
        }

        .tabs {
          display: flex;
          justify-content: center;
          gap: 2px;
          margin-bottom: 20px;
          background-color: #f3f4f6;
          border-radius: 8px;
          padding: 4px;
        }

        .tab-button {
          flex: 1;
          max-width: none;
          padding: 12px 16px;
          font-size: 14px;
          font-weight: 600;
          border: none;
          border-radius: 6px;
          background-color: transparent;
          color: #6b7280;
          transition: all 0.2s ease;
        }

        .tab-button.active {
          background-color: #416955;
          color: white;
          border: none;
        }

        .tab-button:hover:not(.active) {
          background-color: #e5e7eb;
          color: #374151;
        }

        .form-field {
          margin-bottom: 18px;
          display: flex;
          flex-direction: column;
          align-items: stretch;
          gap: 8px;
          min-height: auto;
          background-color: #f9fafb;
          padding: 12px;
          border-radius: 8px;
          border: 1px solid #e5e7eb;
        }

        .form-field label {
          font-size: 14px;
          font-weight: 600;
          color: #374151;
          min-width: auto;
          flex-shrink: 0;
          margin-bottom: 4px;
        }

        .form-field input[type="text"],
        .form-field input[type="number"] {
          width: 100%;
          max-width: none;
          padding: 12px 14px;
          font-size: 16px;
          border: 1px solid #d1d5db;
          border-radius: 8px;
          background-color: white;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .form-field select {
          width: 100%;
          padding: 12px 14px;
          font-size: 16px;
          border: 1px solid #d1d5db;
          border-radius: 8px;
          background-color: white;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .form-field span {
          font-size: 13px;
          color: #6b7280;
          margin-left: 0;
          font-style: italic;
        }

        /* Gender radio buttons - horizontal layout */
        .form-field:has(input[type="radio"]) {
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;
          gap: 16px;
          padding: 12px 16px;
        }

        .form-field input[type="radio"] {
          margin-right: 6px;
          margin-left: 0;
          accent-color: #416955;
          width: 18px;
          height: 18px;
        }

        /* Height and weight input containers */
        #height-us,
        #height-metric,
        #weight-us,
        #weight-metric {
          display: flex !important;
          align-items: center;
          gap: 8px;
          flex-wrap: nowrap;
        }

        #height-us input,
        #height-metric input,
        #weight-us input,
        #weight-metric input {
          flex: 1;
          min-width: 80px;
          max-width: none;
        }

        .form-buttons {
          display: flex;
          gap: 12px;
          margin-top: 24px;
          justify-content: center;
        }

        .calculate-button,
        .clear-button {
          flex: 1;
          max-width: none;
          padding: 14px 20px;
          font-size: 16px;
          font-weight: 600;
          border-radius: 8px;
          min-height: 48px;
        }

        .settings-link {
          text-align: center;
          margin: 20px 0;
        }

        .settings-link a {
          display: inline-block;
          padding: 12px 20px;
          background-color: #f8f9fa;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          color: #416955;
          text-decoration: none;
          font-weight: 600;
          font-size: 14px;
          transition: all 0.2s ease;
        }

        .settings-link a:hover {
          background-color: #416955;
          color: white;
          border-color: #416955;
        }

        .settings-container {
          margin-top: 20px;
          padding: 16px;
          border-radius: 12px;
          background-color: #ffffff;
          border: 1px solid #e5e7eb;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .settings-header {
          margin: -16px -16px 20px -16px;
          padding: 16px 20px;
          border-radius: 12px 12px 0 0;
          background-color: #416955;
        }

        .settings-header h3 {
          font-size: 16px;
          color: white;
          margin: 0;
        }

        .collapse-button {
          color: white;
          font-size: 18px;
        }

        .settings-section {
          margin-bottom: 20px;
          background-color: #f9fafb;
          padding: 12px;
          border-radius: 8px;
          border: 1px solid #e5e7eb;
        }

        .settings-section h3 {
          font-size: 14px;
          margin-bottom: 12px;
          color: #374151;
          font-weight: 600;
        }

        .radio-group {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .radio-option {
          display: flex;
          align-items: center;
          gap: 10px;
          padding: 10px 12px;
          background-color: white;
          border: 1px solid #e5e7eb;
          border-radius: 6px;
          transition: all 0.2s ease;
        }

        .radio-option:hover {
          background-color: #f8f9fa;
          border-color: #416955;
        }

        .radio-option input[type="radio"] {
          margin: 0;
          accent-color: #416955;
        }

        .radio-option label {
          margin: 0;
          font-size: 14px;
          cursor: pointer;
          color: #374151;
          font-weight: 500;
        }

        .results-container {
          margin-top: 20px;
          padding: 0;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .results-header {
          padding: 16px 20px;
        }

        .results-header h2 {
          font-size: 20px;
          margin: 0;
        }

        .results-content {
          padding: 16px;
        }

        .results-description {
          font-size: 14px;
          margin-bottom: 16px;
          line-height: 1.5;
        }

        .bmr-info-section {
          padding: 15px !important;
          margin-bottom: 20px !important;
        }

        .bmr-info-section h3 {
          font-size: 16px !important;
          margin-bottom: 12px !important;
        }

        .bmr-info-section h4 {
          font-size: 14px !important;
          margin-bottom: 8px !important;
        }

        .bmr-info-section > div > div {
          grid-template-columns: 1fr !important;
          gap: 8px !important;
        }

        .bmr-info-section > div > div > div {
          padding: 8px !important;
          font-size: 12px !important;
        }

        /* Mobile-first responsive table design */
        .results-table {
          width: 100%;
          border-collapse: collapse;
          background-color: white;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          margin-bottom: 20px;
        }

        /* Hide table headers on mobile */
        .results-table thead {
          display: none;
        }

        .results-table tbody {
          display: block;
        }

        .results-table tr {
          display: block;
          margin-bottom: 16px;
          background-color: #f9fafb;
          border-radius: 12px;
          padding: 16px;
          border: 1px solid #e5e7eb;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .results-table tr:hover {
          background-color: #f3f4f6;
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          transition: all 0.2s ease;
        }

        .results-table td {
          display: block;
          text-align: left;
          padding: 8px 0;
          border: none;
          position: relative;
        }

        /* Add labels before each data cell */
        .results-table td:before {
          content: attr(data-label);
          font-weight: 600;
          color: #374151;
          font-size: 13px;
          display: block;
          margin-bottom: 4px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .results-table td:first-child:before {
          content: "Goal";
        }

        .results-table td:nth-child(2):before {
          content: "Daily Calories";
        }

        .results-table td:nth-child(3):before {
          content: "Body Fat Change";
        }

        .goal-label {
          font-size: 16px;
          font-weight: 700;
          color: #111827;
          margin-bottom: 4px;
          display: block;
        }

        .goal-description {
          font-size: 13px;
          color: #6b7280;
          font-style: italic;
          line-height: 1.4;
          margin-bottom: 8px;
        }

        .calorie-value {
          font-size: 24px;
          font-weight: 800;
          color: #416955;
          margin: 8px 0 4px 0;
          display: block;
        }

        .percentage {
          font-size: 14px;
          color: #6b7280;
          font-weight: 600;
          display: block;
        }

        .unit-label {
          font-size: 11px;
          color: #9ca3af;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin-top: 2px;
          display: block;
        }

        /* Special styling for the first column (goal) */
        .results-table td:first-child {
          background-color: #f0f9f4;
          border-radius: 8px;
          padding: 12px;
          margin-bottom: 12px;
          border-left: 4px solid #416955;
        }

        /* Enhanced styling for calorie values */
        .results-table td:nth-child(2) {
          text-align: center;
          background-color: white;
          border-radius: 8px;
          padding: 12px;
          margin-bottom: 12px;
          border: 2px solid #e5e7eb;
        }

        /* Enhanced styling for body fat values */
        .results-table td:nth-child(3) {
          text-align: center;
          background-color: #fef7ff;
          border-radius: 8px;
          padding: 12px;
          border: 2px solid #e879f9;
        }

        .weight-gain-section {
          margin-top: 15px;
        }

        .activity-definitions {
          margin-top: 20px;
          padding: 15px;
        }

        .activity-definitions h3 {
          font-size: 18px;
          margin-bottom: 12px;
        }

        .activity-definitions ul {
          padding-left: 15px;
        }

        .activity-definitions li {
          font-size: 14px;
          margin-bottom: 8px;
        }

        .info-section {
          margin-top: 30px;
          padding: 15px;
        }

        .info-section h2 {
          font-size: 22px;
          margin-bottom: 15px;
        }

        .info-section h3 {
          font-size: 18px;
          margin-bottom: 12px;
        }

        .info-section p {
          font-size: 14px;
          line-height: 1.6;
          margin-bottom: 15px;
        }

        .equations-container {
          margin-top: 20px;
        }

        .equation-card {
          margin-bottom: 20px;
          padding: 15px;
        }

        .equation-card h4 {
          font-size: 16px;
          margin-bottom: 10px;
        }

        .equation-card p {
          font-size: 13px;
          margin-bottom: 8px;
        }

        .equation-note {
          font-size: 12px !important;
        }

        .step-card {
          margin-bottom: 15px;
          padding: 15px;
        }

        .step-card h4 {
          font-size: 16px;
          margin-bottom: 10px;
        }

        .step-card p {
          font-size: 14px;
          line-height: 1.5;
        }

        .warning-note {
          padding: 15px;
          margin: 15px 0;
        }

        .warning-note strong {
          font-size: 14px;
        }

        .warning-note ul {
          padding-left: 15px;
        }

        .warning-note li {
          font-size: 13px;
          margin-bottom: 5px;
        }

        .nutrition-table-container {
          margin: 20px 10px !important;
          padding: 0 10px !important;
          /* max-width: calc(100vw - 20px) !important; */
        }

        .nutrition-table-container h3 {
          font-size: 18px !important;
          margin-bottom: 15px !important;
          text-align: center !important;
        }

        .nutrition-table-container > div {
          margin: 0 !important;
          border-radius: 6px !important;
        }

        .nutrition-table-container table {
          font-size: 11px !important;
          min-width: 500px !important;
        }

        .nutrition-table-container th,
        .nutrition-table-container td {
          padding: 8px 6px !important;
          font-size: 11px !important;
          word-wrap: break-word;
          max-width: 120px;
        }

        .nutrition-table-container th {
          font-size: 12px !important;
          font-weight: 600 !important;
        }

        /* Hide kJ column on small screens to save space */
        .nutrition-table-container table th:last-child,
        .nutrition-table-container table td:last-child {
          display: none;
        }

        /* Improve readability of category headers */
        .nutrition-table-container td[colspan="4"] {
          font-size: 12px !important;
          padding: 10px 8px !important;
          text-align: center !important;
        }
      }

      @media (max-width: 480px) {
        #container {
          margin: 0;
          padding: 0 8px;
        }

        main {
          padding: 0;
        }

        h1 {
          font-size: 22px;
          padding: 0 4px;
          margin-bottom: 10px;
        }

        main > p {
          font-size: 14px;
          line-height: 1.4;
          padding: 0 4px;
          margin-bottom: 16px;
          text-align: center;
        }

        .form-container {
          padding: 16px 12px;
          margin: 12px 4px;
          border-radius: 10px;
        }

        .form-instruction {
          font-size: 14px;
          margin-bottom: 16px;
        }

        .tabs {
          padding: 3px;
          margin-bottom: 16px;
        }

        .tab-button {
          padding: 10px 12px;
          font-size: 13px;
        }

        .form-field {
          margin-bottom: 16px;
          padding: 10px;
          gap: 6px;
        }

        .form-field label {
          font-size: 13px;
        }

        .form-field input[type="text"],
        .form-field input[type="number"] {
          padding: 10px 12px;
          font-size: 15px;
        }

        .form-field select {
          padding: 10px 12px;
          font-size: 14px;
        }

        .form-field span {
          font-size: 12px;
        }

        .form-buttons {
          flex-direction: column;
          gap: 10px;
          margin-top: 20px;
        }

        .calculate-button,
        .clear-button {
          max-width: none;
          padding: 12px 20px;
          font-size: 15px;
          min-height: 44px;
        }

        .settings-container {
          padding: 12px;
          margin-top: 16px;
        }

        .settings-header {
          margin: -12px -12px 16px -12px;
          padding: 12px 16px;
        }

        .settings-section {
          margin-bottom: 16px;
          padding: 10px;
        }

        .settings-section h3 {
          font-size: 13px;
          margin-bottom: 10px;
        }

        .radio-option {
          padding: 8px 10px;
        }

        .radio-option label {
          font-size: 13px;
        }

        /* Enhanced mobile table for very small screens */
        .results-table tr {
          margin-bottom: 12px;
          padding: 12px;
        }

        .results-table td:before {
          font-size: 12px;
          margin-bottom: 3px;
        }

        .goal-label {
          font-size: 15px;
        }

        .goal-description {
          font-size: 12px;
          margin-bottom: 6px;
        }

        .calorie-value {
          font-size: 22px;
          margin: 6px 0 3px 0;
        }

        .percentage {
          font-size: 13px;
        }

        .unit-label {
          font-size: 10px;
        }

        .results-table td:first-child,
        .results-table td:nth-child(2),
        .results-table td:nth-child(3) {
          padding: 10px;
          margin-bottom: 10px;
        }

        .bmr-info-section {
          padding: 10px !important;
        }

        .bmr-info-section h3 {
          font-size: 14px !important;
        }

        .bmr-info-section > div > div > div {
          padding: 6px !important;
          font-size: 11px !important;
        }

        .nutrition-table-container {
          margin: 15px 5px !important;
          padding: 0 5px !important;
        }

        .nutrition-table-container h3 {
          font-size: 16px !important;
          margin-bottom: 12px !important;
        }

        .nutrition-table-container table {
          font-size: 10px !important;
          min-width: 400px !important;
        }

        .nutrition-table-container th,
        .nutrition-table-container td {
          padding: 6px 4px !important;
          font-size: 10px !important;
          max-width: 100px;
        }

        .nutrition-table-container th {
          font-size: 11px !important;
        }

        /* Better spacing for category headers on very small screens */
        .nutrition-table-container td[colspan="4"] {
          font-size: 11px !important;
          padding: 8px 6px !important;
        }

        /* Ensure tables don't break layout on very small screens */
        .nutrition-table-container > div {
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
          scrollbar-width: thin;
        }

        /* Hide kJ column and adjust remaining columns */
        .nutrition-table-container table th:last-child,
        .nutrition-table-container table td:last-child {
          display: none;
        }

        .nutrition-table-container table th:first-child,
        .nutrition-table-container table td:first-child {
          min-width: 120px;
        }

        .nutrition-table-container table th:nth-child(2),
        .nutrition-table-container table td:nth-child(2) {
          min-width: 100px;
        }

        .nutrition-table-container table th:nth-child(3),
        .nutrition-table-container table td:nth-child(3) {
          min-width: 80px;
          text-align: center !important;
        }
      }

      /* Tablet specific styles */
      @media (min-width: 769px) and (max-width: 1024px) {
        #container {
          max-width: 95%;
          padding: 20px;
        }

        .form-container {
          padding: 30px 25px;
          margin: 25px auto;
          max-width: 600px;
        }

        .form-field {
          margin-bottom: 22px;
          min-height: 55px;
        }

        .form-field label {
          min-width: 100px;
          font-size: 16px;
        }

        .form-field input[type="text"],
        .form-field input[type="number"] {
          max-width: 140px;
          padding: 11px 14px;
        }

        .form-field select {
          padding: 11px 14px;
          font-size: 16px;
        }

        .form-buttons {
          gap: 15px;
        }

        .calculate-button,
        .clear-button {
          max-width: 180px;
          padding: 15px 24px;
          font-size: 17px;
        }

        .nutrition-table-container {
          margin: 30px auto !important;
          padding: 0 20px !important;
          max-width: 900px !important;
        }

        .nutrition-table-container table {
          font-size: 13px;
        }

        .nutrition-table-container th,
        .nutrition-table-container td {
          padding: 10px 8px !important;
          font-size: 12px !important;
        }

        .nutrition-table-container th {
          font-size: 13px !important;
        }
      }

      /* Large screen optimizations */
      @media (min-width: 1200px) {
        .container {
          max-width: 1200px;
        }

        .form-grid {
          grid-template-columns: 1fr 1fr 1fr;
        }

        .nutrition-table-container {
          /* margin: 40px auto !important; */
          /* max-width: 1100px !important; */
          /* padding: 0 30px !important; */
        }

        .nutrition-table-container table {
          font-size: 15px;
        }

        .nutrition-table-container th,
        .nutrition-table-container td {
          padding: 12px 16px !important;
          font-size: 14px !important;
        }

        .nutrition-table-container th {
          font-size: 16px !important;
        }

        .nutrition-table-container h3 {
          font-size: 24px !important;
          margin-bottom: 25px !important;
        }
      }

      /* Responsive improvements for height/weight inputs */
      @media (max-width: 768px) {
        #height-us,
        #height-metric,
        #weight-us,
        #weight-metric {
          display: flex !important;
          flex-wrap: wrap;
          align-items: center;
          gap: 8px;
        }

        #height-us input,
        #height-metric input,
        #weight-us input,
        #weight-metric input {
          flex: 1;
          min-width: 80px;
          max-width: 120px;
        }

        #height-us span,
        #height-metric span,
        #weight-us span,
        #weight-metric span {
          margin: 0;
          white-space: nowrap;
        }

        .body-fat-input {
          display: flex !important;
          align-items: center;
          gap: 8px;
          margin-top: 8px;
        }

        .body-fat-input input {
          flex: 1;
          max-width: 100px;
        }

        .body-fat-input span {
          margin: 0;
        }
      }

      /* Enhanced table responsiveness */
      @media (max-width: 768px) {
        .results-table-container {
          overflow: visible;
          padding: 0;
        }

        /* Ensure mobile table layout is maintained */
        .results-table {
          min-width: auto;
          width: 100%;
        }

        /* Keep nutrition table scrollable */
        .nutrition-table-container > div {
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
          margin: 0 -15px;
          /* padding: 0 15px; */
        }

        .nutrition-table-container table {
          min-width: 500px;
        }

        /* Add smooth scrolling for better UX */
        .results-container {
          scroll-behavior: smooth;
        }

        /* Improve spacing between table rows */
        .results-table tr:last-child {
          margin-bottom: 0;
        }
      }

      /* Enhanced nutrition table styles for all screen sizes */
      .nutrition-table-container {
        position: relative;
        margin: 20px 0;
      }

      .nutrition-table-container table {
        background-color: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .nutrition-table-container th {
        background-color: #416955 !important;
        color: white !important;
        font-weight: 600;
        text-align: center;
        position: sticky;
        top: 0;
        z-index: 10;
      }

      .nutrition-table-container td {
        background-color: white;
        transition: background-color 0.2s ease;
      }

      .nutrition-table-container tr:hover td {
        background-color: #f8f9fa;
      }

      .nutrition-table-container tr:nth-child(even) td {
        background-color: #f9fafb;
      }

      .nutrition-table-container tr:nth-child(even):hover td {
        background-color: #f1f3f4;
      }

      /* Category header styling */
      .nutrition-table-container td[colspan="4"] {
        background-color: #f0f9f4 !important;
        color: #416955 !important;
        font-weight: 600 !important;
        text-align: center !important;
        border-top: 2px solid #416955 !important;
        border-bottom: 1px solid #bbf7d0 !important;
      }

      .nutrition-table-container td[colspan="4"]:hover {
        background-color: #e6f7ea !important;
      }

      /* Mobile-specific table improvements */
      @media (max-width: 768px) {
        .nutrition-table-container {
          margin: 16px 0;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .nutrition-table-container > div {
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
          scrollbar-width: thin;
          scrollbar-color: #416955 #f1f1f1;
        }

        .nutrition-table-container > div::-webkit-scrollbar {
          height: 6px;
        }

        .nutrition-table-container > div::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        .nutrition-table-container > div::-webkit-scrollbar-thumb {
          background: #416955;
          border-radius: 3px;
        }

        .nutrition-table-container > div::-webkit-scrollbar-thumb:hover {
          background: #2d4a3a;
        }
      }

      /* Enhanced mobile form styling */
      @media (max-width: 768px) {
        /* Form field focus states */
        .form-field input[type="text"]:focus,
        .form-field input[type="number"]:focus,
        .form-field select:focus {
          outline: none;
          border-color: #416955;
          box-shadow: 0 0 0 3px rgba(65, 105, 85, 0.1);
        }

        /* Better spacing for form elements */
        .form-field:last-of-type {
          margin-bottom: 25px;
        }

        /* Improved button styling */
        .calculate-button {
          background-color: #416955;
          color: white;
          border: none;
          font-weight: 500;
          transition: background-color 0.2s ease;
        }

        .calculate-button:hover {
          background-color: #2d4a3a;
        }

        .clear-button {
          background-color: #f3f4f6;
          color: #111827;
          border: 1px solid #d1d5db;
          font-weight: 500;
          transition: all 0.2s ease;
        }

        .clear-button:hover {
          background-color: #e5e7eb;
          border-color: #9ca3af;
        }

        /* Settings improvements */
        .settings-section {
          margin-bottom: 20px;
        }

        .settings-section h3 {
          font-size: 16px;
          margin-bottom: 12px;
          color: #111827;
        }

        .body-fat-input {
          margin-left: 0 !important;
          margin-top: 10px;
        }

        .body-fat-input input {
          width: 80px !important;
          max-width: 80px !important;
        }
      }

      /* Enhanced Mobile Touch and Accessibility */
      @media (max-width: 768px) {
        .calculate-button,
        .clear-button,
        .collapse-button {
          min-height: 48px; /* Enhanced touch target size */
          touch-action: manipulation;
          cursor: pointer;
        }

        .tab-button {
          min-height: 48px;
          touch-action: manipulation;
          cursor: pointer;
        }

        .settings-link a {
          display: inline-block;
          padding: 12px 16px;
          min-height: 48px;
          line-height: 24px;
          touch-action: manipulation;
        }

        /* Enhanced form field styling for mobile */
        .form-field input[type="text"]:focus,
        .form-field input[type="number"]:focus,
        .form-field select:focus {
          outline: none;
          border-color: #416955;
          box-shadow: 0 0 0 3px rgba(65, 105, 85, 0.15);
          transform: scale(1.02);
          transition: all 0.2s ease;
        }

        /* Improved radio button styling */
        .form-field input[type="radio"] {
          transform: scale(1.2);
          margin-right: 8px;
        }

        /* Better visual hierarchy */
        .form-field {
          transition: all 0.2s ease;
        }

        .form-field:hover {
          background-color: #f3f4f6;
          border-color: #d1d5db;
        }

        /* Enhanced button states */
        .calculate-button:active {
          transform: scale(0.98);
          background-color: #2d4a3a;
        }

        .clear-button:active {
          transform: scale(0.98);
          background-color: #d1d5db;
        }

        .tab-button:active {
          transform: scale(0.98);
        }

        /* Improved spacing for better touch targets */
        .form-field:has(input[type="radio"]) {
          padding: 16px;
          gap: 20px;
        }

        /* Better visual feedback for active states */
        .tab-button.active {
          box-shadow: 0 2px 4px rgba(65, 105, 85, 0.2);
        }
      }

      .settings-section {
        margin-bottom: 30px;
      }

      .settings-section:last-child {
        margin-bottom: 0;
      }

      .settings-section h3 {
        color: #111827;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 16px;
      }

      .radio-group {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .radio-option {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 8px 0;
      }

      .radio-option input[type="radio"] {
        margin: 0;
        accent-color: #416955;
        width: 18px;
        height: 18px;
      }

      .radio-option label {
        color: #111827;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        flex: 1;
      }

      .body-fat-input {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-top: 8px;
        margin-left: 28px;
      }

      .body-fat-input input {
        width: 80px;
        background-color: white;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 16px;
        transition: border-color 0.2s ease;
      }

      .body-fat-input input:focus {
        outline: none;
        border-color: #416955;
        box-shadow: 0 0 0 3px rgba(65, 105, 85, 0.1);
      }

      .body-fat-input span {
        color: #6b7280;
        font-size: 14px;
      }

      .info-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        background-color: #e5e7eb;
        color: #6b7280;
        border-radius: 50%;
        font-size: 12px;
        font-weight: bold;
        cursor: help;
        margin-left: 8px;
      }

      .info-icon:hover {
        background-color: #416955;
        color: white;
      }

      /* Enhanced Results Styles */
      .results-container {
        margin-top: 30px;
        display: none;
      }

      .results-header {
        background-color: #416955;
        color: white;
        padding: 16px 20px;
        border-radius: 8px 8px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .results-header h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }

      .print-icon {
        background: none;
        border: none;
        color: white;
        font-size: 18px;
        cursor: pointer;
        padding: 8px;
        border-radius: 4px;
        transition: background-color 0.2s ease;
      }

      .print-icon:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .results-content {
        background-color: white;
        border: 1px solid #e5e7eb;
        border-top: none;
        border-radius: 0 0 8px 8px;
        padding: 20px;
      }

      .results-description {
        color: #111827;
        font-size: 16px;
        margin-bottom: 20px;
        line-height: 1.6;
      }

      .results-table {
        border-collapse: collapse;
        width: 100%;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        overflow: hidden;
      }

      .results-table th,
      .results-table td {
        border: 1px solid #e5e7eb;
        padding: 16px 20px;
        text-align: left;
        vertical-align: top;
      }

      .results-table th {
        background-color: #f9fafb;
        font-weight: 600;
        color: #111827;
        font-size: 16px;
        text-align: center;
        border-bottom: 2px solid #416955;
      }

      .results-table td {
        background-color: #ffffff;
        transition: background-color 0.2s ease;
      }

      .results-table tr:hover td {
        background-color: #f8f9fa;
      }

      .results-table tr:nth-child(even) td {
        background-color: #f9fafb;
      }

      .results-table tr:nth-child(even):hover td {
        background-color: #f1f3f4;
      }

      .goal-label {
        font-weight: 600;
        color: #111827;
        font-size: 16px;
        margin-bottom: 4px;
      }

      .goal-description {
        font-size: 14px;
        color: #6b7280;
        margin-top: 2px;
        font-style: italic;
      }

      .calorie-value {
        font-size: 28px;
        font-weight: 700;
        color: #416955;
        margin-bottom: 4px;
        text-align: center;
      }

      .percentage {
        font-size: 14px;
        color: #6b7280;
        text-align: center;
        font-weight: 500;
      }

      .unit-label {
        font-size: 12px;
        color: #9ca3af;
        margin-top: 2px;
        text-align: center;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      /* Desktop table layout - restore traditional table */
      @media (min-width: 769px) {
        .results-table {
          font-size: 16px;
          border-collapse: collapse;
          display: table;
        }

        .results-table thead {
          display: table-header-group;
        }

        .results-table tbody {
          display: table-row-group;
        }

        .results-table tr {
          display: table-row;
          margin-bottom: 0;
          background-color: transparent;
          border-radius: 0;
          padding: 0;
          border: none;
          box-shadow: none;
        }

        .results-table tr:hover {
          background-color: #f8f9fa;
          transform: none;
          box-shadow: none;
        }

        .results-table th,
        .results-table td {
          display: table-cell;
          padding: 20px 24px;
          border: 1px solid #e5e7eb;
          background-color: transparent;
          border-radius: 0;
          margin-bottom: 0;
        }

        .results-table th {
          background-color: #f9fafb !important;
          font-weight: 600;
          color: #111827 !important;
          font-size: 18px;
          text-align: center;
          border-bottom: 2px solid #416955;
        }

        .results-table td {
          background-color: #ffffff;
          transition: background-color 0.2s ease;
        }

        .results-table tr:nth-child(even) td {
          background-color: #f9fafb;
        }

        .results-table tr:nth-child(even):hover td {
          background-color: #f1f3f4;
        }

        /* Remove mobile-specific pseudo-elements */
        .results-table td:before {
          display: none;
        }

        .calorie-value {
          font-size: 32px;
          margin: 0;
          display: inline;
        }

        .goal-label {
          font-size: 18px;
          margin-bottom: 4px;
          display: block;
        }

        .goal-description {
          font-size: 15px;
          margin-bottom: 0;
        }

        .percentage {
          font-size: 15px;
          display: inline;
        }

        .unit-label {
          font-size: 13px;
          margin-top: 2px;
          display: block;
        }

        /* Better column alignment for desktop */
        .results-table th:first-child,
        .results-table td:first-child {
          text-align: left;
          width: 35%;
        }

        .results-table th:nth-child(2),
        .results-table td:nth-child(2),
        .results-table th:nth-child(3),
        .results-table td:nth-child(3) {
          text-align: center;
          width: 32.5%;
        }
      }

      .weight-gain-toggle {
        margin-top: 20px;
      }

      .toggle-link {
        color: #416955;
        text-decoration: none;
        font-weight: 500;
        font-size: 16px;
        cursor: pointer;
      }

      .toggle-link:hover {
        color: #2d4a3a;
        text-decoration: underline;
      }

      .weight-gain-section {
        display: none;
        margin-top: 20px;
      }

      /* Types Section Styles */
      .types-section {
        margin-top: 40px;
        padding: 30px;
        background-color: #ffffff;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .types-section h2 {
        color: #416955;
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      .calorie-types {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-top: 24px;
      }
      .type-card {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 20px;
      }
      .type-card h4 {
        color: #416955;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 12px;
      }
      .thermic-effect {
        margin-top: 30px;
        background-color: #f0f9f4;
        border: 1px solid #bbf7d0;
        border-radius: 8px;
        padding: 20px;
      }
      .thermic-effect h3 {
        color: #416955;
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 12px;
      }

      /* Additional Mobile Utilities */
      @media (max-width: 768px) {
        /* Prevent horizontal scroll */
        html,
        body {
          overflow-x: hidden;
          width: 100%;
        }

        /* Ensure all containers respect mobile width */
        * {
          max-width: 100%;
        }

        /* Better mobile typography */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          word-wrap: break-word;
          hyphens: auto;
        }

        /* Improved mobile form validation styles */
        .form-field input:invalid {
          border-color: #ef4444;
          box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-field input:valid {
          border-color: #10b981;
        }

        /* Enhanced mobile loading states */
        .calculate-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }

        /* Better mobile error states */
        .error-message {
          color: #ef4444;
          font-size: 14px;
          margin-top: 4px;
          padding: 8px 12px;
          background-color: #fef2f2;
          border: 1px solid #fecaca;
          border-radius: 6px;
        }

        /* Mobile-optimized animations */
        @media (prefers-reduced-motion: reduce) {
          *,
          *::before,
          *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }
        }

        /* Improved mobile focus management */
        .form-field input:focus,
        .form-field select:focus {
          scroll-margin-top: 100px;
        }

        /* Better mobile table scrolling */
        .results-table-container {
          position: relative;
          overflow: visible;
          scroll-behavior: smooth;
          padding: 0;
        }

        /* Ensure mobile cards don't overflow */
        .results-table tr {
          max-width: 100%;
          word-wrap: break-word;
        }

        /* Mobile-specific print styles */
        @media print {
          .navbar,
          .settings-link,
          .form-buttons {
            display: none !important;
          }

          .form-container {
            box-shadow: none !important;
            border: 1px solid #000 !important;
          }
        }
      }

      /* High DPI display optimizations */
      @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        .logo img {
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <img
            width="200"
            src="./assets/august_logo_green_nd4fn9.svg"
            alt="Calculator Logo"
          />
        </div>
        <div class="nav">
          <a
            href="https://app.meetaugust.ai/redirect/wa?message=Hello%20August"
            class="talk-to-august"
            >Talk to August</a
          >
        </div>
      </div>
    </header>
    <div id="container">
      <main>
        <h1>Advanced Daily Calorie Requirements Calculator</h1>
        <p>
          Discover your personalized daily calorie needs with our
          scientifically-backed calculator. Whether you're planning for weight
          management, muscle building, or maintaining optimal health, our tool
          provides precise calorie recommendations based on your unique
          metabolic profile and lifestyle factors.
        </p>
        <section class="form-container">
          <div class="tabs">
            <button class="tab-button" data-unit="us">US Units</button>
            <button class="tab-button active" data-unit="metric">
              Metric Units
            </button>
          </div>
          <p class="form-instruction">
            Enter your personal details below and click Calculate to get your
            customized calorie recommendations
          </p>
          <form id="calorie-form">
            <div class="form-field">
              <label for="age">Age</label>
              <input type="text" id="age" value="25" />
              <span>ages 15 - 80</span>
            </div>
            <div class="form-field">
              <label>Gender</label>
              <input type="radio" name="gender" value="male" checked /> male
              <input type="radio" name="gender" value="female" /> female
            </div>
            <div class="form-field">
              <label for="height">Height</label>
              <!-- US Units (feet and inches) -->
              <div id="height-us" style="display: none">
                <input
                  type="text"
                  id="height-feet"
                  value="5"
                  style="width: 60px"
                />
                <span>feet</span>
                <input
                  type="text"
                  id="height-inches"
                  value="10"
                  style="width: 60px"
                />
                <span>inches</span>
              </div>
              <!-- Metric Units (cm) -->
              <div id="height-metric" style="display: block">
                <input type="text" id="height-cm" value="180" />
                <span>cm</span>
              </div>
            </div>
            <div class="form-field">
              <label for="weight">Weight</label>
              <!-- US Units (pounds) -->
              <div id="weight-us" style="display: none">
                <input type="text" id="weight-lbs" value="165" />
                <span>pounds</span>
              </div>
              <!-- Metric Units (kg) -->
              <div id="weight-metric" style="display: block">
                <input type="text" id="weight-kg" value="65" />
                <span>kg</span>
              </div>
            </div>
            <div class="form-field">
              <label for="activity">Activity</label>
              <select id="activity">
                <option value="sedentary">
                  Sedentary: little or no exercise
                </option>
                <option value="light">
                  Lightly active: light exercise 1-3 days/week
                </option>
                <option value="moderate" selected>
                  Moderately active: moderate exercise 3-5 days/week
                </option>
                <option value="very">
                  Very active: hard exercise 6-7 days/week
                </option>
                <option value="super">
                  Super active: very hard exercise, physical job
                </option>
              </select>
            </div>
            <div class="settings-link">
              <a href="#" id="settings-link">+ Settings</a>
            </div>

            <!-- Settings Section (inline) -->
            <div id="settings-container" class="settings-container">
              <div class="settings-header">
                <h3>Settings</h3>
                <button class="collapse-button" id="collapse-settings">
                  −
                </button>
              </div>

              <div class="settings-section">
                <h3>Results unit:</h3>
                <div class="radio-group">
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="calories-unit"
                      name="results-unit"
                      value="calories"
                      checked
                    />
                    <label for="calories-unit">Calories</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="kilojoules-unit"
                      name="results-unit"
                      value="kilojoules"
                    />
                    <label for="kilojoules-unit">Kilojoules</label>
                  </div>
                </div>
              </div>

              <div class="settings-section">
                <h3>
                  Body Fat Percentage:
                  <span
                    class="info-icon"
                    title="Enter your body fat percentage for more accurate body composition calculations"
                    >?</span
                  >
                </h3>
                <div
                  class="body-fat-input"
                  style="display: flex; margin-left: 0"
                >
                  <input
                    type="number"
                    id="user-body-fat"
                    min="5"
                    max="50"
                    step="0.1"
                    value="40"
                  />
                  <span>%</span>
                </div>
              </div>

              <div class="settings-section">
                <h3>
                  BMR estimation formula:
                  <span
                    class="info-icon"
                    title="Choose the formula for calculating your Basal Metabolic Rate"
                    >?</span
                  >
                </h3>
                <div class="radio-group">
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="mifflin-formula"
                      name="bmr-formula"
                      value="mifflin"
                    />
                    <label for="mifflin-formula">Mifflin St Jeor</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="harris-formula"
                      name="bmr-formula"
                      value="harris"
                    />
                    <label for="harris-formula">Revised Harris-Benedict</label>
                  </div>
                  <div class="radio-option">
                    <input
                      type="radio"
                      id="katch-formula"
                      name="bmr-formula"
                      value="katch"
                      checked
                    />
                    <label for="katch-formula">Katch-McArdle</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-buttons">
              <button type="button" class="calculate-button">
                Calculate ▶
              </button>
              <button type="button" class="clear-button">Clear</button>
            </div>
          </form>
        </section>

        <!-- Enhanced Results Section -->
        <div id="results-container" class="results-container">
          <div class="results-header">
            <h2>Result</h2>
            <button
              class="print-icon"
              onclick="window.print()"
              title="Print results"
            >
              🖨
            </button>
          </div>
          <div class="results-content">
            <p class="results-description">
              Your personalized calorie targets are calculated using advanced
              metabolic formulas. These recommendations provide daily calorie
              intake guidelines tailored to your specific goals - whether you
              want to maintain your current weight, achieve sustainable weight
              loss, or support healthy weight gain.
            </p>

            <!-- BMR and Activity Information -->
            <div
              class="bmr-info-section"
              style="
                margin-bottom: 30px;
                padding: 20px;
                background-color: #f9fafb;
                border-radius: 8px;
                border: 1px solid #e5e7eb;
              "
            >
              <h3
                style="
                  color: #416955;
                  font-size: 18px;
                  font-weight: 600;
                  margin-bottom: 16px;
                "
              >
                Basal Metabolic Rate (BMR):
                <span id="bmr-value" style="color: #111827">1,650</span>
                calories/day
              </h3>

              <div style="margin-bottom: 16px">
                <h4
                  style="
                    color: #111827;
                    font-size: 16px;
                    font-weight: 600;
                    margin-bottom: 8px;
                  "
                >
                  Activity Levels:
                </h4>
                <div
                  style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 8px;
                    font-size: 14px;
                  "
                >
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Sedentary:</strong> little or no exercise
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Light:</strong> exercise 1-3 times/week
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Moderate:</strong> exercise 4-5 times/week
                  </div>
                  <div
                    id="active-highlight"
                    style="
                      padding: 8px;
                      background-color: #e0f2e7;
                      border-radius: 4px;
                      border: 2px solid #416955;
                    "
                  >
                    <strong>Active:</strong> daily exercise or intense exercise
                    3-4 times/week
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Very Active:</strong> intense exercise 6-7
                    times/week
                  </div>
                  <div
                    style="
                      padding: 8px;
                      background-color: white;
                      border-radius: 4px;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    <strong>Extra Active:</strong> very intense exercise daily,
                    or physical job
                  </div>
                </div>
              </div>
            </div>

            <table class="results-table" id="results-table">
              <thead>
                <tr>
                  <th style="width: 40%">Goal</th>
                  <th style="width: 30%">Calories</th>
                  <th style="width: 30%">Body Fat</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td data-label="Goal">
                    <div class="goal-label">Maintain weight</div>
                  </td>
                  <td data-label="Daily Calories">
                    <div class="calorie-value" id="maintain-calories">
                      2,549
                    </div>
                    <div class="percentage">100%</div>
                    <div class="unit-label">Calories/day</div>
                  </td>
                  <td data-label="Body Fat Change">
                    <div class="calorie-value" id="maintain-bodyfat">20</div>
                    <div class="percentage">100%</div>
                    <div class="unit-label">%</div>
                  </td>
                </tr>
                <tr style="background-color: #f9fafb">
                  <td data-label="Goal">
                    <div class="goal-label">Mild weight loss</div>
                    <div class="goal-description">0.5 lb/week</div>
                  </td>
                  <td data-label="Daily Calories">
                    <div class="calorie-value" id="mild-loss-calories">
                      2,299
                    </div>
                    <div class="percentage">90%</div>
                    <div class="unit-label">Calories/day</div>
                  </td>
                  <td data-label="Body Fat Change">
                    <div class="calorie-value" id="mild-loss-bodyfat">19</div>
                    <div class="percentage">95%</div>
                    <div class="unit-label">%</div>
                  </td>
                </tr>
                <tr>
                  <td data-label="Goal">
                    <div class="goal-label">Weight loss</div>
                    <div class="goal-description">1 lb/week</div>
                  </td>
                  <td data-label="Daily Calories">
                    <div class="calorie-value" id="weight-loss-calories">
                      2,049
                    </div>
                    <div class="percentage">80%</div>
                    <div class="unit-label">Calories/day</div>
                  </td>
                  <td data-label="Body Fat Change">
                    <div class="calorie-value" id="weight-loss-bodyfat">18</div>
                    <div class="percentage">90%</div>
                    <div class="unit-label">%</div>
                  </td>
                </tr>
                <tr style="background-color: #f9fafb">
                  <td data-label="Goal">
                    <div class="goal-label">Extreme weight loss</div>
                    <div class="goal-description">2 lb/week</div>
                  </td>
                  <td data-label="Daily Calories">
                    <div class="calorie-value" id="extreme-loss-calories">
                      1,549
                    </div>
                    <div class="percentage">61%</div>
                    <div class="unit-label">Calories/day</div>
                  </td>
                  <td data-label="Body Fat Change">
                    <div class="calorie-value" id="extreme-loss-bodyfat">
                      16
                    </div>
                    <div class="percentage">80%</div>
                    <div class="unit-label">%</div>
                  </td>
                </tr>
              </tbody>
            </table>

            <div
              class="weight-gain-toggle"
              style="text-align: left; margin-top: 20px; padding-left: 0"
            >
              <a
                href="javascript:void(0)"
                class="toggle-link"
                id="weight-gain-toggle"
                >Show info for weight gain</a
              >
            </div>

            <div class="weight-gain-section" id="weight-gain-section">
              <h3 style="color: #416955; margin-bottom: 16px">
                Weight Gain Information
              </h3>
              <table class="results-table">
                <thead>
                  <tr>
                    <th style="width: 40%">Goal</th>
                    <th style="width: 30%">Calories</th>
                    <th style="width: 30%">Body Fat</th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="background-color: #f9fafb">
                    <td data-label="Goal">
                      <div class="goal-label">Mild weight gain</div>
                      <div class="goal-description">0.5 lb/week</div>
                    </td>
                    <td data-label="Daily Calories">
                      <div class="calorie-value" id="mild-gain-calories">
                        2,799
                      </div>
                      <div class="percentage">110%</div>
                      <div class="unit-label">Calories/day</div>
                    </td>
                    <td data-label="Body Fat Change">
                      <div class="calorie-value" id="mild-gain-bodyfat">21</div>
                      <div class="percentage">105%</div>
                      <div class="unit-label">%</div>
                    </td>
                  </tr>
                  <tr>
                    <td data-label="Goal">
                      <div class="goal-label">Weight gain</div>
                      <div class="goal-description">1 lb/week</div>
                    </td>
                    <td data-label="Daily Calories">
                      <div class="calorie-value" id="weight-gain-calories">
                        3,049
                      </div>
                      <div class="percentage">120%</div>
                      <div class="unit-label">Calories/day</div>
                    </td>
                    <td data-label="Body Fat Change">
                      <div class="calorie-value" id="weight-gain-bodyfat">
                        22
                      </div>
                      <div class="percentage">110%</div>
                      <div class="unit-label">%</div>
                    </td>
                  </tr>
                  <tr style="background-color: #f9fafb">
                    <td data-label="Goal">
                      <div class="goal-label">Fast Weight gain</div>
                      <div class="goal-description">2 lb/week</div>
                    </td>
                    <td data-label="Daily Calories">
                      <div class="calorie-value" id="fast-gain-calories">
                        3,549
                      </div>
                      <div class="percentage">139%</div>
                      <div class="unit-label">Calories/day</div>
                    </td>
                    <td data-label="Body Fat Change">
                      <div class="calorie-value" id="fast-gain-bodyfat">24</div>
                      <div class="percentage">120%</div>
                      <div class="unit-label">%</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div id="result" class="result"></div>
        <div class="activity-definitions">
          <h3>Physical Activity Guidelines</h3>
          <ul>
            <li>
              <strong>Light Exercise:</strong> 20-40 minutes of
              moderate-intensity activities like walking or gentle yoga.
            </li>
            <li>
              <strong>Moderate Exercise:</strong> 30-60 minutes of activities
              that raise your heart rate, such as brisk walking or cycling.
            </li>
            <li>
              <strong>Vigorous Exercise:</strong> 45-90 minutes of
              high-intensity training, sports, or demanding physical activities.
            </li>
            <li>
              <strong>Professional/Athletic Training:</strong> 2+ hours of
              intensive training or physically demanding occupational work.
            </li>
          </ul>
        </div>

        <!-- Nutritional Reference Tables Section -->
        <section class="info-section">
          <h2>Complete Nutritional Reference Guide</h2>
          <p>
            Use these comprehensive tables to make informed dietary choices and
            better understand the caloric content of everyday foods, meal
            planning strategies, and exercise energy expenditure.
          </p>

          <!-- Food Calorie Table -->
          <div
            class="nutrition-table-container"
            style="
              /* margin: 30px auto; */
              text-align: center;
              /* max-width: 1000px; */
              /* padding: 0 15px; */
            "
          >
            <h3
              style="
                color: #416955;
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 20px;
                text-align: center;
              "
            >
              Caloric Content of Popular Foods
            </h3>

            <div
              style="
                overflow-x: auto;
                margin-bottom: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              "
            >
              <table
                style="
                  width: 100%;
                  border-collapse: collapse;
                  font-size: 14px;
                  margin: 0 auto;
                  min-width: 600px;
                "
              >
                <thead>
                  <tr style="background-color: #416955; color: white">
                    <th
                      style="
                        padding: 12px;
                        text-align: left;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Food Item
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: left;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Portion Size
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: left;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Calories
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: left;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      kJ
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Fresh Fruits
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Mango
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 medium (5 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      135
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      565
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Kiwi
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 large (3 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      56
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      234
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Blueberries
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 cup
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      84
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      351
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Avocado
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1/2 medium (3 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      160
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      670
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Cherries
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 cup
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      97
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      406
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Fresh Vegetables
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Sweet Potato
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 medium (5 oz.)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      112
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      469
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Bell Pepper
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 cup sliced
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      28
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      117
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Spinach
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      2 cups fresh
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      14
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      59
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Zucchini
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 cup sliced
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      19
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      80
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Cauliflower
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 cup
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      25
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      105
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Protein Sources
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Salmon, grilled
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      3 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      175
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      732
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Turkey breast
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      3 oz.
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      125
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      523
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Greek yogurt
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      6 oz. container
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      130
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      544
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Almonds
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 oz. (23 nuts)
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      164
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      686
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Lentils, cooked
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1/2 cup
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      115
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      481
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Grains & Starches
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Quinoa, cooked
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 cup
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      222
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      929
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Oatmeal
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 cup cooked
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      154
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      644
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Whole wheat pasta
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 cup cooked
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      174
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      728
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Brown rice
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 cup cooked
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      218
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      912
                    </td>
                  </tr>

                  <tr style="background-color: #f0f9f4">
                    <td
                      colspan="4"
                      style="
                        padding: 8px 12px;
                        font-weight: 600;
                        color: #416955;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Beverages
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Green tea
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 cup
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      2
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      8
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Almond milk
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 cup
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      39
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      163
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Coconut water
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      1 cup
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      46
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      192
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      Red wine
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      5 oz. glass
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      125
                    </td>
                    <td style="padding: 8px 12px; border: 1px solid #e5e7eb">
                      523
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p style="font-size: 12px; color: #6b7280; font-style: italic">
              * 1 cup = approximately 240ml, 1 oz. = approximately 28g
            </p>
          </div>

          <!-- Sample Meal Plans -->
          <div
            class="nutrition-table-container"
            style="
              /* margin: 40px auto 30px auto; */
              text-align: center;
              /* max-width: 1000px; */
              /* padding: 0 15px; */
            "
          >
            <h3
              style="
                color: #416955;
                font-size: 22px;
                font-weight: 600;
                margin-bottom: 20px;
                text-align: center;
              "
            >
              Strategic Meal Planning: 1300, 1600, and 2100 Calorie Daily Plans
            </h3>

            <div
              style="
                overflow-x: auto;
                margin-bottom: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              "
            >
              <table
                style="
                  width: 100%;
                  border-collapse: collapse;
                  font-size: 14px;
                  margin: 0 auto;
                  min-width: 700px;
                "
              >
                <thead>
                  <tr style="background-color: #416955; color: white">
                    <th
                      style="
                        padding: 12px;
                        text-align: left;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      Meal Period
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: left;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      1300 Cal Plan
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: left;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      1600 Cal Plan
                    </th>
                    <th
                      style="
                        padding: 12px;
                        text-align: left;
                        border: 1px solid #e5e7eb;
                      "
                    >
                      2100 Cal Plan
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Breakfast
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Steel-cut oats (140)<br />
                      Almond milk (25)<br />
                      Raspberries (65)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Protein smoothie (180)<br />
                      Spinach (10)<br />
                      Chia seeds (60)<br />
                      Mango (70)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Avocado toast (220)<br />
                      Scrambled eggs (140)<br />
                      Mixed berries (80)<br />
                      Cashews (190)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Morning Snack
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Herbal tea (0)<br />
                      Rice cakes (70)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Green tea (2)<br />
                      Almonds (85)<br />
                      Apple slices (55)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Protein bar (180)<br />
                      Coconut water (45)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Total Morning
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      300 Calories
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      462 Calories
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      855 Calories
                    </td>
                  </tr>

                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Lunch
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Mediterranean salad (280)<br />
                      Hummus (90)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Quinoa bowl (320)<br />
                      Grilled vegetables (80)<br />
                      Tahini dressing (110)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Grilled salmon (240)<br />
                      Sweet potato (150)<br />
                      Asparagus (35)<br />
                      Olive oil drizzle (120)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Afternoon Snack
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Cucumber slices (15)<br />
                      Tzatziki (55)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Trail mix (120)<br />
                      Herbal tea (0)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Greek yogurt (130)<br />
                      Granola (110)<br />
                      Honey drizzle (65)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Total Midday
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      440 Calories
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      630 Calories
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      850 Calories
                    </td>
                  </tr>

                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        background-color: #f0f9f4;
                        color: #416955;
                      "
                    >
                      Dinner
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Lentil soup (220)<br />
                      Mixed greens (30)<br />
                      Balsamic vinaigrette (45)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Turkey meatballs (280)<br />
                      Zucchini noodles (25)<br />
                      Marinara sauce (70)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Grass-fed beef (300)<br />
                      Roasted vegetables (120)<br />
                      Quinoa pilaf (180)
                    </td>
                  </tr>
                  <tr style="background-color: #f8f9fa">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      Evening Snack
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Chamomile tea (0)<br />
                      Dark chocolate (85)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Cottage cheese (110)<br />
                      Walnuts (95)
                    </td>
                    <td style="padding: 12px; border: 1px solid #e5e7eb">
                      Protein shake (160)<br />
                      Banana (105)<br />
                      Almond butter (95)
                    </td>
                  </tr>
                  <tr>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                        color: #416955;
                      "
                    >
                      Total Evening
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      380 Calories
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      580 Calories
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      960 Calories
                    </td>
                  </tr>
                  <tr style="background-color: #416955; color: white">
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      DAILY TOTAL
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      1,120 Calories
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      1,672 Calories
                    </td>
                    <td
                      style="
                        padding: 12px;
                        border: 1px solid #e5e7eb;
                        font-weight: 600;
                      "
                    >
                      2,665 Calories
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>

        <!-- Food Energy Converter Section -->
        <!-- <section class="converter-section">
          <h2>Food Energy Converter</h2>
          <p>
            The following converter can be used to convert between Calories and
            other common food energy units.
          </p>

          <div class="converter-container">
            <div class="converter-input">
              <input type="number" id="converter-value" value="1" />
              <select id="from-unit">
                <option value="kcal">Calorie [Nutritional, kcal]</option>
                <option value="cal">calorie [cal]</option>
                <option value="kj">Kilojoules [kJ]</option>
                <option value="j">joules [J]</option>
              </select>
            </div>
            <div class="converter-equals">=</div>
            <div class="converter-output">
              <span id="converted-value">4.1868</span>
              <select id="to-unit">
                <option value="kcal">Calorie [Nutritional, kcal]</option>
                <option value="cal">calorie [cal]</option>
                <option value="kj" selected>Kilojoules [kJ]</option>
                <option value="j">joules [J]</option>
              </select>
            </div>
          </div>
        </section> -->

        <!-- Related Calculators Section -->
        <!-- <section class="related-section">
          <h2>Related Calculators</h2>
          <div class="related-links">
            <a href="#" class="related-link">BMI Calculator</a>
            <a href="#" class="related-link">Body Fat Calculator</a>
            <a href="#" class="related-link">Ideal Weight Calculator</a>
          </div>
        </section> -->

        <!-- Comprehensive Information Section -->
        <section class="info-section">
          <h2>
            Master Your Metabolism: The Science Behind Calorie Calculation
          </h2>
          <p>
            Understanding your metabolic needs is the foundation of effective
            nutrition planning. Our advanced calculator employs multiple
            validated scientific formulas to determine your Basal Metabolic Rate
            (BMR) - the energy your body requires for essential functions at
            rest. Perfect for fitness enthusiasts, nutrition professionals,
            weight management goals, and anyone committed to optimizing their
            health through precise caloric planning.
          </p>

          <div class="equations-container">
            <h3>Three Proven Metabolic Formulas for Maximum Accuracy</h3>
            <p>
              Our calculator integrates three scientifically-validated
              equations, each optimized for different body compositions and
              metabolic profiles:
            </p>

            <div class="equation-card">
              <h4>1. Mifflin-St Jeor Formula (Gold Standard for Precision)</h4>
              <p>
                <strong>Male Formula:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) + 5
              </p>
              <p>
                <strong>Female Formula:</strong> BMR = 10 × weight (kg) + 6.25 ×
                height (cm) - 5 × age (years) - 161
              </p>
              <p class="equation-note">
                Recognized as the most reliable metabolic rate predictor for
                diverse populations, this formula delivers exceptional accuracy
                across various age groups and body types.
              </p>
            </div>

            <div class="equation-card">
              <h4>2. Enhanced Harris-Benedict Method (Modernized Classic)</h4>
              <p>
                <strong>Male Calculation:</strong> BMR = 13.397 × weight + 4.799
                × height - 5.677 × age + 88.362
              </p>
              <p>
                <strong>Female Calculation:</strong> BMR = 9.247 × weight +
                3.098 × height - 4.330 × age + 447.593
              </p>
              <p class="equation-note">
                An advanced refinement of the pioneering 1919 Harris-Benedict
                equation, this updated version provides enhanced precision for
                contemporary metabolic assessment.
              </p>
            </div>

            <div class="equation-card">
              <h4>
                3. Katch-McArdle Method (Athletic & Body Composition Focused)
              </h4>
              <p>BMR = 370 + 21.6 × (1 − body fat percentage) × weight (kg)</p>
              <p class="equation-note">
                This specialized formula incorporates lean muscle mass
                calculations, making it exceptionally accurate for athletes,
                bodybuilders, and individuals with known body fat measurements.
              </p>
            </div>

            <div class="info-text">
              <h3>
                Transforming BMR into Your Total Daily Energy Expenditure (TDEE)
              </h3>
              <p>
                Your BMR represents your baseline metabolic needs. To determine
                your complete daily calorie requirements, we apply
                activity-specific multipliers:
              </p>

              <div
                class="activity-table"
                style="
                  margin: 20px 0;
                  background-color: #f9fafb;
                  border: 1px solid #e5e7eb;
                  border-radius: 8px;
                  padding: 20px;
                "
              >
                <table style="width: 100%; border-collapse: collapse">
                  <thead>
                    <tr style="background-color: #416955; color: white">
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Activity Level
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Description
                      </th>
                      <th
                        style="
                          padding: 12px;
                          text-align: left;
                          border: 1px solid #e5e7eb;
                        "
                      >
                        Multiplier
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Sedentary
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        Little to no exercise
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.2
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Lightly active
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        Light exercise 1–3 days/week
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.375
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Moderately active
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        Moderate exercise 3–5 days/week
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.55
                      </td>
                    </tr>
                    <tr style="background-color: #f8f9fa">
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Very active
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        Intense exercise 6–7 days/week
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.725
                      </td>
                    </tr>
                    <tr>
                      <td
                        style="
                          padding: 8px;
                          border: 1px solid #e5e7eb;
                          font-weight: 500;
                        "
                      >
                        Super active
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        Very intense daily exercise or job
                      </td>
                      <td style="padding: 8px; border: 1px solid #e5e7eb">
                        1.9
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <p>
                This calculation yields your Total Daily Energy Expenditure
                (TDEE)—the precise number of calories required to maintain your
                current body weight and energy balance.
              </p>

              <h3>Strategic Weight Management: Evidence-Based Approaches</h3>
              <p>
                For sustainable weight reduction, create a moderate caloric
                deficit of approximately 500 calories daily, targeting a healthy
                1-pound (0.45 kg) weekly loss. This approach aligns with
                nutritional science recommendations for long-term success and
                metabolic preservation.
              </p>

              <div class="warning-note">
                <strong
                  >⚠️ Critical Guidelines: Avoid excessive caloric restrictions
                  exceeding 1,000 calories daily or rapid weight loss beyond 2
                  pounds weekly, which may cause:</strong
                >
                <ul style="margin: 8px 0; padding-left: 20px">
                  <li>Metabolic rate suppression</li>
                  <li>Lean muscle tissue degradation</li>
                  <li>Essential nutrient depletion</li>
                  <li>Rebound weight gain from unsustainable practices</li>
                </ul>
                <p style="margin-top: 12px">
                  Optimal results come from integrating nutrient-dense foods,
                  consistent physical activity, and proper hydration for
                  sustainable wellness transformation.
                </p>
              </div>

              <h3>Professional Nutrition Optimization Strategies</h3>
              <ul
                style="
                  list-style-type: disc;
                  padding-left: 20px;
                  margin: 16px 0;
                "
              >
                <li>
                  Prioritize nutrient density alongside caloric awareness for
                  optimal health outcomes.
                </li>
                <li>
                  Ensure adequate protein, fiber, and micronutrient intake for
                  metabolic efficiency.
                </li>
                <li>
                  Reject restrictive dieting in favor of sustainable lifestyle
                  modifications.
                </li>
                <li>
                  Develop consistent daily practices rather than pursuing
                  short-term fixes.
                </li>
              </ul>

              <p style="font-weight: 500; color: #416955; margin-top: 20px">
                This advanced calculator serves as your comprehensive foundation
                for achieving personalized nutrition goals, whether targeting
                body composition changes, performance enhancement, or optimal
                health maintenance.
              </p>
            </div>
          </div>
        </section>

        <!-- Calorie Counting Section -->
        <section class="counting-section">
          <h2>
            Precision Calorie Tracking: Your Roadmap to Successful Weight
            Management
          </h2>

          <div class="steps-container">
            <div class="step-card">
              <h4>1. Determine Your Metabolic Baseline</h4>
              <p>
                Utilize our integrated BMR calculation system featuring multiple
                validated formulas. For enhanced accuracy with known body
                composition, select the Katch-McArdle method. Remember: these
                provide scientific estimates—individual metabolic variations
                mean a 500-calorie daily reduction may not always equal exactly
                one pound weekly loss.
              </p>
            </div>

            <div class="step-card">
              <h4>2. Establish Achievable Weight Management Targets</h4>
              <p>
                Understanding that one pound equals approximately 3,500
                calories, a sustainable 500-calorie daily deficit typically
                produces 1 lb/week progress. Maintain safe parameters by
                avoiding losses exceeding 2 lb/week. For accelerated goals, seek
                guidance from qualified nutrition professionals.
              </p>
            </div>

            <div class="step-card">
              <h4>3. Implement Comprehensive Monitoring Systems</h4>
              <p>
                Deploy digital tracking tools, nutrition apps, or detailed food
                journals to monitor caloric intake and physical activity.
                Precision improves with practice—begin with measured portions,
                then develop intuitive estimation skills.
              </p>
              <p>
                Conduct weekly weigh-ins at consistent times (morning, pre-meal)
                to minimize hydration and digestive variables. Focus on weekly
                trends rather than daily fluctuations for accurate progress
                assessment.
              </p>
            </div>

            <div class="step-card">
              <h4>4. Optimize Through Continuous Assessment</h4>
              <p>
                Body weight represents only one metric; prioritize body
                composition analysis (muscle-to-fat ratios) for comprehensive
                progress evaluation. Adjust caloric intake based on measurable
                results and maintain consistency for sustainable transformation.
              </p>
            </div>
          </div>

          <div class="info-text">
            <h3>The Science of Caloric Balance: Strengths and Limitations</h3>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>
                <strong>Energy Balance Fundamentals:</strong> Weight management
                follows the principle of energy input versus energy expenditure.
              </li>
              <li>
                <strong>Thermic Effect of Food:</strong> Whole food processing
                requires additional metabolic energy—your digestive system burns
                calories during nutrient breakdown.
              </li>
              <li>
                <strong>Satiety and Food Quality:</strong> Nutrient-dense whole
                foods provide superior satiation per calorie compared to
                processed snacks.
              </li>
            </ul>

            <p style="font-style: italic; color: #374151; margin: 16px 0">
              Case study: A famously extreme "Twinkie diet" user lost 27 pounds
              in 2 months by strict calorie counting—but such diets aren't
              healthy or sustainable. Long-term health risks remain.
            </p>

            <h4 style="color: #416955; margin-top: 20px">
              Bonus benefits of tracking calories:
            </h4>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>Builds nutritional awareness</li>
              <li>Improves portion control</li>
              <li>Helps you connect foods with exercise effort</li>
            </ul>
          </div>
        </section>

        <!-- Zigzag Calorie Cycling Section -->
        <section class="zigzag-section">
          <h2>Zigzag Calorie Cycling (AKA Calorie "Zig-Zagging")</h2>
          <p>
            To avoid plateaus from adaptation, try alternating daily calorie
            intake.
          </p>

          <div class="zigzag-explanation">
            <div class="example-card">
              <h4>Example</h4>
              <p><strong>Weekly target:</strong> 14,000 kcal</p>
              <ul style="margin: 12px 0; padding-left: 20px">
                <li>
                  <strong>Option A:</strong> 2,300 kcal for 3 days + 1,775 kcal
                  for 4 days
                </li>
                <li><strong>Option B:</strong> 2,000 kcal every day</li>
              </ul>
              <p>
                Same weekly total, but the body stays guessing. Adjust the gap
                (typically 200–300 kcal) based on activity level.
              </p>
            </div>

            <div class="benefits-card">
              <h4>The goal?</h4>
              <ul style="margin: 12px 0; padding-left: 20px">
                <li>Better metabolic flexibility</li>
                <li>
                  Diet flexibility—like planning for social meals or cheat days
                </li>
                <li>Prevents metabolic adaptation</li>
                <li>May help break weight loss plateaus</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- Calorie Requirements Section -->
        <section class="requirements-section">
          <h2>How Many Calories Do You Really Need?</h2>
          <p>Calorie needs vary by:</p>

          <div class="factors-grid">
            <div class="factor-card">
              <h4>Factors That Influence Calorie Needs</h4>
              <ul>
                <li>Age, sex, weight, height</li>
                <li>Activity level</li>
                <li>Health status</li>
              </ul>
            </div>

            <div class="guidelines-card">
              <h4>General Guidelines</h4>
              <p><strong>Men:</strong> ~2,000–3,000 kcal/day</p>
              <p>
                <strong>Women:</strong> ~1,600–2,400 kcal/day (per U.S.
                guidelines)
              </p>
            </div>

            <div class="minimum-card">
              <h4>Minimum Safe Intake</h4>
              <p><strong>Women:</strong> ~1,200 kcal/day</p>
              <p><strong>Men:</strong> ~1,500 kcal/day</p>
              <p class="warning">Any lower should be medically supervised</p>
            </div>
          </div>

          <div class="warning-note" style="margin-top: 20px">
            <p>
              <strong>⚠️ Important:</strong> Over-restricting can backfire—your
              body slows metabolism and affects physical and mental wellness.
            </p>
          </div>
        </section>

        <!-- Calorie Types Section -->
        <section class="types-section">
          <h2>Not All Calories Are Equal</h2>
          <p>Calories come from:</p>
          <ul style="list-style-type: disc; padding-left: 20px; margin: 16px 0">
            <li>Protein</li>
            <li>Carbohydrates</li>
            <li>Fat</li>
            <li>Alcohol (empty calories)</li>
          </ul>

          <p>
            Nutrition labels aren't perfect—actual absorbed calories can vary
            significantly. Foods that take more chewing (like veggies and whole
            grains) support fullness and slightly increase digestion energy use.
          </p>

          <div class="calorie-types">
            <div class="type-card">
              <h4>High‑Calorie Foods</h4>
              <p>Fats, oils—but also healthy choices like:</p>
              <ul>
                <li>Avocados (healthy option)</li>
                <li>Nuts and seeds (healthy option)</li>
                <li>Fried foods</li>
                <li>Sugary foods</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>Low‑Calorie Foods</h4>
              <ul>
                <li>Many vegetables</li>
                <li>Some fruits</li>
                <li>Lean proteins</li>
                <li>Whole grains</li>
                <li>Leafy greens</li>
              </ul>
            </div>

            <div class="type-card">
              <h4>Empty Calories</h4>
              <ul>
                <li>Sugary drinks</li>
                <li>Processed snacks</li>
                <li>Added sugars</li>
                <li>Solid fats</li>
                <li>Alcohol</li>
              </ul>
            </div>
          </div>

          <div class="thermic-effect">
            <h3>Caloric Quality Matters</h3>
            <p>
              Drinks can account for ~21% of daily calories—opting for water,
              unsweetened tea, or coffee can make a big difference.
            </p>

            <h4 style="color: #416955; margin-top: 16px">
              Build a Balanced, Sustainable Plan
            </h4>
            <ul
              style="list-style-type: disc; padding-left: 20px; margin: 16px 0"
            >
              <li>Focus on whole, minimally processed foods</li>
              <li>Don't overconsume sugar or refined snacks</li>
              <li>
                Choose eating habits—like plate size and mindful chewing—that
                promote natural portion control
              </li>
              <li>
                Combine calorie counting with regular physical activity for best
                results
              </li>
            </ul>
          </div>
        </section>

        <!-- Final Takeaway Section -->
        <section class="info-section" style="margin-top: 40px">
          <h2>Final Takeaway</h2>
          <p>
            There's no one-size-fits-all "health" solution. Whether you choose
            straight calorie counting, zigzag methods, or another path, the best
            approach is one you can stick to long term.
          </p>
          <p style="font-weight: 500; color: #416955">
            Track wisely, eat mindfully, and build sustainable habits for
            lifelong health and nutrition.
          </p>
        </section>
      </main>
    </div>
    <script>
      // Global settings
      var settings = {
        resultsUnit: "calories",
        bmrFormula: "katch",
        bodyFat: 20, // Default body fat for Katch-McArdle
        userBodyFat: 20, // Default user body fat
        unitSystem: "metric", // "metric" or "us"
      };

      // Unit conversion functions
      function convertHeight(value, fromUnit, toUnit) {
        if (fromUnit === toUnit) return value;

        if (fromUnit === "cm" && toUnit === "feet-inches") {
          var totalInches = value / 2.54;
          var feet = Math.floor(totalInches / 12);
          var inches = Math.round(totalInches % 12);
          return { feet: feet, inches: inches };
        } else if (fromUnit === "feet-inches" && toUnit === "cm") {
          return Math.round(value * 2.54);
        }
        return value;
      }

      function convertWeight(value, fromUnit, toUnit) {
        if (fromUnit === toUnit) return value;

        if (fromUnit === "kg" && toUnit === "lbs") {
          return Math.round(value * 2.20462);
        } else if (fromUnit === "lbs" && toUnit === "kg") {
          return Math.round(value / 2.20462);
        }
        return value;
      }

      // Tab switching with unit conversion
      var tabs = document.querySelectorAll(".tab-button");
      tabs.forEach(function (tab) {
        tab.addEventListener("click", function () {
          var newUnit = this.getAttribute("data-unit");

          // Don't do anything if clicking the same unit
          if (settings.unitSystem === newUnit) return;

          // Update active tab
          tabs.forEach(function (t) {
            t.classList.remove("active");
          });
          this.classList.add("active");

          // Convert and switch units
          switchUnits(newUnit);
          settings.unitSystem = newUnit;
        });
      });

      function switchUnits(unitSystem) {
        if (unitSystem === "us") {
          // Convert from metric to US
          var currentHeightCm =
            parseFloat(document.getElementById("height-cm").value) || 180;
          var currentWeightKg =
            parseFloat(document.getElementById("weight-kg").value) || 65;

          // Convert height
          var heightFeetInches = convertHeight(
            currentHeightCm,
            "cm",
            "feet-inches"
          );
          document.getElementById("height-feet").value = heightFeetInches.feet;
          document.getElementById("height-inches").value =
            heightFeetInches.inches;

          // Convert weight
          var weightLbs = convertWeight(currentWeightKg, "kg", "lbs");
          document.getElementById("weight-lbs").value = weightLbs;

          // Show US inputs, hide metric
          document.getElementById("height-us").style.display = "block";
          document.getElementById("height-metric").style.display = "none";
          document.getElementById("weight-us").style.display = "block";
          document.getElementById("weight-metric").style.display = "none";
        } else {
          // Convert from US to metric
          var currentFeet =
            parseFloat(document.getElementById("height-feet").value) || 5;
          var currentInches =
            parseFloat(document.getElementById("height-inches").value) || 10;
          var currentWeightLbs =
            parseFloat(document.getElementById("weight-lbs").value) || 165;

          // Convert height
          var totalInches = currentFeet * 12 + currentInches;
          var heightCm = convertHeight(totalInches, "feet-inches", "cm");
          document.getElementById("height-cm").value = heightCm;

          // Convert weight
          var weightKg = convertWeight(currentWeightLbs, "lbs", "kg");
          document.getElementById("weight-kg").value = weightKg;

          // Show metric inputs, hide US
          document.getElementById("height-us").style.display = "none";
          document.getElementById("height-metric").style.display = "block";
          document.getElementById("weight-us").style.display = "none";
          document.getElementById("weight-metric").style.display = "block";
        }
      }

      // Enhanced calculation function matching calculator.net exactly
      function calculateBMR(gender, weight, height, age, bodyFat, equation) {
        let BMR;

        // Calculate BMR based on selected equation
        switch (equation) {
          case "mifflin":
            if (gender === "male") {
              BMR = 10 * weight + 6.25 * height - 5 * age + 5;
            } else {
              BMR = 10 * weight + 6.25 * height - 5 * age - 161;
            }
            break;
          case "harris":
            if (gender === "male") {
              BMR = 13.397 * weight + 4.799 * height - 5.677 * age + 88.362;
            } else {
              BMR = 9.247 * weight + 3.098 * height - 4.33 * age + 447.593;
            }
            break;
          case "katch":
            if (
              bodyFat === undefined ||
              bodyFat === null ||
              bodyFat < 0 ||
              bodyFat > 100
            ) {
              throw new Error(
                "Valid body fat percentage (0-100) required for Katch-McArdle"
              );
            }
            BMR = 370 + 21.6 * (1 - bodyFat / 100) * weight;
            break;
          default:
            throw new Error('Equation must be "mifflin", "harris", or "katch"');
        }

        return BMR;
      }

      function calculateTDEE(bmr, activityLevel) {
        // Activity factors matching calculator.net exactly based on the reference URL
        const activityFactors = {
          sedentary: 1.2,
          light: 1.375,
          moderate: 1.465, // This matches the reference URL cactivity=1.465
          very: 1.725,
          super: 1.9,
        };

        const activityFactor = activityFactors[activityLevel];
        if (!activityFactor) {
          throw new Error("Invalid activity level");
        }

        return bmr * activityFactor;
      }

      function calculateCalorieTargets(tdee) {
        // Calculate targets based on percentages matching calculator.net
        return {
          maintain: Math.round(tdee),
          mildLoss: Math.round(tdee * 0.86), // 86% for mild weight loss (0.25 kg/week)
          weightLoss: Math.round(tdee * 0.72), // 72% for weight loss (0.5 kg/week)
          extremeLoss: Math.round(tdee * 0.44), // 44% for extreme weight loss (1 kg/week)
          mildGain: Math.round(tdee * 1.14), // 114% for mild weight gain
          weightGain: Math.round(tdee * 1.28), // 128% for weight gain
          fastGain: Math.round(tdee * 1.56), // 156% for fast weight gain
        };
      }

      // Convert calories to kilojoules
      function convertToKilojoules(calories) {
        return Math.round(calories * 4.1868);
      }

      // Format number with commas
      function formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      }

      // Get activity multiplier
      function getActivityMultiplier(activity) {
        switch (activity) {
          case "sedentary":
            return 1.2;
          case "light":
            return 1.375;
          case "moderate":
            return 1.465; // Updated to match calculator.net
          case "very":
            return 1.725;
          case "super":
            return 1.9;
          default:
            return 1.465;
        }
      }

      // Update activity level highlighting
      function updateActivityHighlight(selectedActivity) {
        // Reset all activity cards to default style
        var activityCards = document.querySelectorAll(
          '.bmr-info-section [style*="padding: 8px"]'
        );
        activityCards.forEach(function (card) {
          card.style.backgroundColor = "white";
          card.style.border = "1px solid #e5e7eb";
        });

        // Highlight the selected activity
        var activityMap = {
          sedentary: 0,
          light: 1,
          moderate: 2,
          very: 4,
          super: 5,
        };

        var selectedIndex = activityMap[selectedActivity];
        if (selectedIndex !== undefined && activityCards[selectedIndex]) {
          activityCards[selectedIndex].style.backgroundColor = "#e0f2e7";
          activityCards[selectedIndex].style.border = "2px solid #416955";
        }
      }

      // Calculate estimated body fat changes
      function calculateBodyFatChanges(
        currentBodyFat,
        currentWeight,
        targetWeight
      ) {
        if (!currentBodyFat || currentBodyFat <= 0) {
          return currentBodyFat || 20; // Default estimate if no body fat provided
        }

        // Estimate body fat change based on weight change
        // This is a simplified calculation - actual body fat changes depend on many factors
        var weightChange = targetWeight - currentWeight;
        var bodyFatChange = weightChange * 0.1; // Rough estimate: 10% of weight change affects body fat
        var newBodyFat = currentBodyFat + bodyFatChange;

        // Keep within reasonable bounds
        return Math.max(5, Math.min(50, newBodyFat));
      }

      // Update results display
      function updateResults(tdee, userWeight, userBodyFat, userActivity) {
        var unit =
          settings.resultsUnit === "calories"
            ? "Calories/day"
            : "Kilojoules/day";
        var conversionFactor = settings.resultsUnit === "calories" ? 1 : 4.1868;

        // Calculate all the different calorie targets using the correct percentages
        var calorieTargets = calculateCalorieTargets(tdee);
        var targets = {
          maintain: Math.round(calorieTargets.maintain * conversionFactor),
          mildLoss: Math.round(calorieTargets.mildLoss * conversionFactor),
          weightLoss: Math.round(calorieTargets.weightLoss * conversionFactor),
          extremeLoss: Math.round(
            calorieTargets.extremeLoss * conversionFactor
          ),
          mildGain: Math.round(calorieTargets.mildGain * conversionFactor),
          weightGain: Math.round(calorieTargets.weightGain * conversionFactor),
          fastGain: Math.round(calorieTargets.fastGain * conversionFactor),
        };

        // Calculate estimated body fat percentages for different goals
        var bodyFatTargets = {
          maintain: userBodyFat || 20,
          mildLoss: calculateBodyFatChanges(
            userBodyFat || 20,
            userWeight,
            userWeight - 2
          ), // ~2 lbs loss
          weightLoss: calculateBodyFatChanges(
            userBodyFat || 20,
            userWeight,
            userWeight - 4
          ), // ~4 lbs loss
          extremeLoss: calculateBodyFatChanges(
            userBodyFat || 20,
            userWeight,
            userWeight - 8
          ), // ~8 lbs loss
          mildGain: calculateBodyFatChanges(
            userBodyFat || 20,
            userWeight,
            userWeight + 2
          ), // ~2 lbs gain
          weightGain: calculateBodyFatChanges(
            userBodyFat || 20,
            userWeight,
            userWeight + 4
          ), // ~4 lbs gain
          fastGain: calculateBodyFatChanges(
            userBodyFat || 20,
            userWeight,
            userWeight + 8
          ), // ~8 lbs gain
        };

        // Update calorie displays
        document.getElementById("maintain-calories").textContent = formatNumber(
          targets.maintain
        );
        document.getElementById("mild-loss-calories").textContent =
          formatNumber(targets.mildLoss);
        document.getElementById("weight-loss-calories").textContent =
          formatNumber(targets.weightLoss);
        document.getElementById("extreme-loss-calories").textContent =
          formatNumber(targets.extremeLoss);
        document.getElementById("mild-gain-calories").textContent =
          formatNumber(targets.mildGain);
        document.getElementById("weight-gain-calories").textContent =
          formatNumber(targets.weightGain);
        document.getElementById("fast-gain-calories").textContent =
          formatNumber(targets.fastGain);

        // Update body fat displays
        document.getElementById("maintain-bodyfat").textContent =
          Math.round(bodyFatTargets.maintain * 10) / 10;
        document.getElementById("mild-loss-bodyfat").textContent =
          Math.round(bodyFatTargets.mildLoss * 10) / 10;
        document.getElementById("weight-loss-bodyfat").textContent =
          Math.round(bodyFatTargets.weightLoss * 10) / 10;
        document.getElementById("extreme-loss-bodyfat").textContent =
          Math.round(bodyFatTargets.extremeLoss * 10) / 10;
        document.getElementById("mild-gain-bodyfat").textContent =
          Math.round(bodyFatTargets.mildGain * 10) / 10;
        document.getElementById("weight-gain-bodyfat").textContent =
          Math.round(bodyFatTargets.weightGain * 10) / 10;
        document.getElementById("fast-gain-bodyfat").textContent =
          Math.round(bodyFatTargets.fastGain * 10) / 10;

        // Update BMR display
        var bmrValue = Math.round(tdee / getActivityMultiplier(userActivity));
        document.getElementById("bmr-value").textContent =
          formatNumber(bmrValue);

        // Update activity level highlighting
        updateActivityHighlight(userActivity);

        // Update unit labels for calories
        var calorieUnitLabels = document.querySelectorAll(".unit-label");
        calorieUnitLabels.forEach(function (label) {
          if (
            label.textContent.includes("Calories") ||
            label.textContent.includes("Kilojoules")
          ) {
            label.textContent = unit;
          }
        });

        // Calculate and update calorie percentages
        document
          .querySelector("#mild-loss-calories")
          .parentNode.querySelector(".percentage").textContent =
          Math.round((targets.mildLoss / targets.maintain) * 100) + "%";
        document
          .querySelector("#weight-loss-calories")
          .parentNode.querySelector(".percentage").textContent =
          Math.round((targets.weightLoss / targets.maintain) * 100) + "%";
        document
          .querySelector("#extreme-loss-calories")
          .parentNode.querySelector(".percentage").textContent =
          Math.round((targets.extremeLoss / targets.maintain) * 100) + "%";
        document
          .querySelector("#mild-gain-calories")
          .parentNode.querySelector(".percentage").textContent =
          Math.round((targets.mildGain / targets.maintain) * 100) + "%";
        document
          .querySelector("#weight-gain-calories")
          .parentNode.querySelector(".percentage").textContent =
          Math.round((targets.weightGain / targets.maintain) * 100) + "%";
        document
          .querySelector("#fast-gain-calories")
          .parentNode.querySelector(".percentage").textContent =
          Math.round((targets.fastGain / targets.maintain) * 100) + "%";

        // Calculate and update body fat percentages
        var baseBodyFat = bodyFatTargets.maintain;
        document
          .querySelector("#mild-loss-bodyfat")
          .parentNode.querySelector(".percentage").textContent =
          Math.round((bodyFatTargets.mildLoss / baseBodyFat) * 100) + "%";
        document
          .querySelector("#weight-loss-bodyfat")
          .parentNode.querySelector(".percentage").textContent =
          Math.round((bodyFatTargets.weightLoss / baseBodyFat) * 100) + "%";
        document
          .querySelector("#extreme-loss-bodyfat")
          .parentNode.querySelector(".percentage").textContent =
          Math.round((bodyFatTargets.extremeLoss / baseBodyFat) * 100) + "%";
        document
          .querySelector("#mild-gain-bodyfat")
          .parentNode.querySelector(".percentage").textContent =
          Math.round((bodyFatTargets.mildGain / baseBodyFat) * 100) + "%";
        document
          .querySelector("#weight-gain-bodyfat")
          .parentNode.querySelector(".percentage").textContent =
          Math.round((bodyFatTargets.weightGain / baseBodyFat) * 100) + "%";
        document
          .querySelector("#fast-gain-bodyfat")
          .parentNode.querySelector(".percentage").textContent =
          Math.round((bodyFatTargets.fastGain / baseBodyFat) * 100) + "%";

        // Show results container and hide old result
        document.getElementById("results-container").style.display = "block";
        document.getElementById("result").style.display = "none";
      }

      // Helper function to get current height and weight in metric units
      function getCurrentHeightWeight() {
        var height, weight;

        if (settings.unitSystem === "us") {
          // Get US values and convert to metric
          var feet =
            parseFloat(document.getElementById("height-feet").value) || 5;
          var inches =
            parseFloat(document.getElementById("height-inches").value) || 10;
          var totalInches = feet * 12 + inches;
          height = totalInches * 2.54; // Convert to cm

          var weightLbs =
            parseFloat(document.getElementById("weight-lbs").value) || 165;
          weight = weightLbs / 2.20462; // Convert to kg
        } else {
          // Get metric values directly
          height =
            parseFloat(document.getElementById("height-cm").value) || 180;
          weight = parseFloat(document.getElementById("weight-kg").value) || 65;
        }

        return { height: height, weight: weight };
      }

      // Calculate button
      document
        .querySelector(".calculate-button")
        .addEventListener("click", function () {
          try {
            var age = parseInt(document.getElementById("age").value);
            var gender = document.querySelector(
              'input[name="gender"]:checked'
            ).value;
            var heightWeight = getCurrentHeightWeight();
            var height = heightWeight.height;
            var weight = heightWeight.weight;
            var activity = document.getElementById("activity").value;

            // Get body fat from the body fat input field
            var bodyFatInput = document.getElementById("user-body-fat");
            var bodyFat = parseFloat(bodyFatInput.value) || null;

            // Calculate BMR using the new function
            var bmr = calculateBMR(
              gender,
              weight,
              height,
              age,
              bodyFat,
              settings.bmrFormula
            );

            // Calculate TDEE
            var tdee = calculateTDEE(bmr, activity);

            // Update the enhanced results display
            updateResults(tdee, weight, bodyFat, activity);
          } catch (error) {
            alert("Error: " + error.message);
          }
        });

      // Clear button
      document
        .querySelector(".clear-button")
        .addEventListener("click", function () {
          // Reset form values for both unit systems
          document.getElementById("age").value = "25";
          document.querySelector(
            'input[name="gender"][value="male"]'
          ).checked = true;

          // Reset height values
          document.getElementById("height-feet").value = "5";
          document.getElementById("height-inches").value = "10";
          document.getElementById("height-cm").value = "180";

          // Reset weight values
          document.getElementById("weight-lbs").value = "165";
          document.getElementById("weight-kg").value = "65";

          // Reset activity
          document.getElementById("activity").selectedIndex = 2; // Moderate activity

          // Clear results
          document.getElementById("result").innerHTML = "";
          document.getElementById("results-container").style.display = "none";
          document.getElementById("result").style.display = "block";
        });

      // Settings inline functionality
      var settingsContainer = document.getElementById("settings-container");
      var settingsLink = document.getElementById("settings-link");
      var collapseButton = document.getElementById("collapse-settings");
      var isSettingsVisible = false;

      settingsLink.addEventListener("click", function (e) {
        e.preventDefault();
        isSettingsVisible = !isSettingsVisible;

        if (isSettingsVisible) {
          settingsContainer.style.display = "block";
          this.textContent = "− Settings";
        } else {
          settingsContainer.style.display = "none";
          this.textContent = "+ Settings";
        }
      });

      // Collapse button functionality
      collapseButton.addEventListener("click", function () {
        settingsContainer.style.display = "none";
        settingsLink.textContent = "+ Settings";
        isSettingsVisible = false;
      });

      // Function to recalculate results if they are currently displayed
      function recalculateIfNeeded() {
        if (
          document.getElementById("results-container").style.display === "block"
        ) {
          // Trigger recalculation
          document.querySelector(".calculate-button").click();
        }
      }

      // Settings form handlers
      document
        .querySelectorAll('input[name="results-unit"]')
        .forEach(function (radio) {
          radio.addEventListener("change", function () {
            settings.resultsUnit = this.value;
            recalculateIfNeeded();
          });
        });

      document
        .querySelectorAll('input[name="bmr-formula"]')
        .forEach(function (radio) {
          radio.addEventListener("change", function () {
            settings.bmrFormula = this.value;
            var bodyFatInput = document.getElementById("body-fat-input");
            if (this.value === "katch") {
              bodyFatInput.style.display = "flex";
              // Set focus to body fat input
              setTimeout(function () {
                document.getElementById("body-fat").focus();
              }, 100);
            } else {
              bodyFatInput.style.display = "none";
            }
            recalculateIfNeeded();
          });
        });

      // Note: body-fat element doesn't exist, using user-body-fat instead
      // This code block is redundant since user-body-fat handler is below

      // User body fat input handler
      document
        .getElementById("user-body-fat")
        .addEventListener("input", function () {
          settings.userBodyFat = parseFloat(this.value) || null;
          recalculateIfNeeded();
        });

      // Weight gain toggle functionality
      var weightGainToggle = document.getElementById("weight-gain-toggle");
      var weightGainSection = document.getElementById("weight-gain-section");
      var isWeightGainVisible = false;

      if (weightGainToggle && weightGainSection) {
        weightGainToggle.addEventListener("click", function (e) {
          e.preventDefault();
          isWeightGainVisible = !isWeightGainVisible;

          if (isWeightGainVisible) {
            weightGainSection.style.display = "block";
            this.textContent = "Hide info for weight gain";
            // Add visual confirmation
            // weightGainSection.style.border = "2px solid #416955";
          } else {
            weightGainSection.style.display = "none";
            this.textContent = "Show info for weight gain";
            weightGainSection.style.border = "none";
          }
        });

        // Initialize weight gain as hidden
        weightGainSection.style.display = "none";
        weightGainToggle.textContent = "Show info for weight gain";
      }

      // Food Energy Converter
      function convertEnergy() {
        var value =
          parseFloat(document.getElementById("converter-value").value) || 0;
        var fromUnit = document.getElementById("from-unit").value;
        var toUnit = document.getElementById("to-unit").value;

        // Conversion factors to kilojoules
        var toKJ = {
          kcal: 4.1868,
          cal: 0.0041868,
          kj: 1,
          j: 0.001,
        };

        // Convert to kilojoules first, then to target unit
        var kj = value * toKJ[fromUnit];
        var result = kj / toKJ[toUnit];

        document.getElementById("converted-value").textContent =
          result.toFixed(4);
      }

      // Add event listeners for converter (with null checks)
      const converterValue = document.getElementById("converter-value");
      const fromUnit = document.getElementById("from-unit");
      const toUnit = document.getElementById("to-unit");

      if (converterValue) {
        converterValue.addEventListener("input", convertEnergy);
      }
      if (fromUnit) {
        fromUnit.addEventListener("change", convertEnergy);
      }
      if (toUnit) {
        toUnit.addEventListener("change", convertEnergy);
      }

      // Initialize converter
      if (converterValue && fromUnit && toUnit) {
        convertEnergy();
      }

      // Header scroll effect
      function handleScroll() {
        const header = document.querySelector("header");
        if (header) {
          if (window.scrollY === 0) {
            header.classList.remove("scrolled");
          } else {
            header.classList.add("scrolled");
          }
        }
      }

      // Add scroll event listener
      window.addEventListener("scroll", handleScroll);

      // Initialize header state
      handleScroll();
      
    </script>
  </body>
</html>
